class ProjectShowcase {
    constructor() {
        this.projects = [];
        this.currentView = 'grid'; // grid or list
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadProjects();
    }

    bindEvents() {
        // 刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadProjects();
        });

        // 视图切换
        document.getElementById('gridToggle').addEventListener('click', () => {
            this.toggleView();
        });

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.filterProjects(e.target.value);
        });

        // 排序功能
        document.getElementById('sortSelect').addEventListener('change', (e) => {
            this.sortProjects(e.target.value);
        });

        // 模态框事件
        document.getElementById('closeModal').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('closeModalBtn').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('openInNewTab').addEventListener('click', () => {
            const iframe = document.getElementById('projectFrame');
            if (iframe.src) {
                window.open(iframe.src, '_blank');
            }
        });

        // 点击模态框背景关闭
        document.getElementById('projectModal').addEventListener('click', (e) => {
            if (e.target.id === 'projectModal') {
                this.closeModal();
            }
        });
    }

    async loadProjects() {
        this.showLoading();

        try {
            // 直接从配置文件读取项目列表
            const projects = await this.loadProjectsFromConfig();
            this.projects = projects;
            this.updateProjectCount();
            this.renderProjects();
            this.updateLastUpdate();
        } catch (error) {
            console.error('加载项目失败:', error);
            this.showEmptyState();
        }
    }

    async loadProjectsFromConfig() {
        try {
            const configResponse = await fetch('project-config.json');
            if (configResponse.ok) {
                const config = await configResponse.json();
                // 转换日期字符串为Date对象
                return config.projects.map(project => ({
                    ...project,
                    lastModified: new Date(project.lastModified)
                }));
            }
        } catch (error) {
            console.log('读取项目配置文件失败:', error);
        }

        // 如果配置文件不存在或读取失败，返回默认项目
        return [
            {
                name: 'Todo应用',
                folder: 'todo-app',
                description: '一个简单的待办事项管理应用，支持添加、删除、标记完成任务',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImEiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2MzY2ZjEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM4YjVjZjYiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+VG9kbyDlupTnlKg8L3RleHQ+PC9zdmc+',
                lastModified: new Date(),
                hasIndex: true,
                tags: ['生产力', '任务管理']
            },
            {
                name: '天气应用',
                folder: 'weather-app',
                description: '实时天气查询应用，支持多城市天气查询和历史记录',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImIiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM3NGI5ZmYiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwOTg0ZTMiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2IpIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5aSp5rCU5bqU55SoPC90ZXh0Pjwvc3ZnPg==',
                lastModified: new Date(),
                hasIndex: true,
                tags: ['天气', '工具']
            },
            {
                name: '计算器',
                folder: 'calculator',
                description: '功能完整的计算器应用，支持基础运算和历史记录',
                thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImMiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2NjdlZWEiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3NjRiYTIiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2MpIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+6K6h566X5ZmoPC90ZXh0Pjwvc3ZnPg==',
                lastModified: new Date(),
                hasIndex: true,
                tags: ['计算', '工具']
            }
        ];
    }

    renderProjects() {
        const grid = document.getElementById('projectGrid');
        const loading = document.getElementById('loadingState');
        const empty = document.getElementById('emptyState');

        loading.classList.add('hidden');

        if (this.projects.length === 0) {
            grid.classList.add('hidden');
            empty.classList.remove('hidden');
            return;
        }

        empty.classList.add('hidden');
        grid.classList.remove('hidden');

        grid.innerHTML = this.projects.map(project => this.createProjectCard(project)).join('');
        
        // 添加淡入动画
        setTimeout(() => {
            grid.querySelectorAll('.project-card').forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });
        }, 50);
    }

    createProjectCard(project) {
        return `
            <div class="project-card bg-white rounded-lg shadow-md overflow-hidden cursor-pointer" 
                 onclick="projectShowcase.openProject('${project.folder}', '${project.name}')">
                <div class="aspect-w-16 aspect-h-9">
                    <img src="${project.thumbnail}" alt="${project.name}" 
                         class="w-full h-48 object-cover">
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">${project.name}</h3>
                    <p class="text-gray-600 mb-4">${project.description}</p>
                    <div class="flex justify-between items-center text-sm text-gray-500">
                        <span>文件夹: ${project.folder}</span>
                        <span>${this.formatDate(project.lastModified)}</span>
                    </div>
                    <div class="mt-4 flex space-x-2">
                        <button onclick="event.stopPropagation(); projectShowcase.openProject('${project.folder}', '${project.name}')" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors">
                            预览
                        </button>
                        <button onclick="event.stopPropagation(); window.open('project/${project.folder}/index.html', '_blank')" 
                                class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm transition-colors">
                            打开
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    openProject(folder, name) {
        const modal = document.getElementById('projectModal');
        const title = document.getElementById('modalTitle');
        const frame = document.getElementById('projectFrame');
        
        title.textContent = name;
        frame.src = `project/${folder}/index.html`;
        modal.classList.remove('hidden');
        
        // 设置新标签页打开按钮
        document.getElementById('openInNewTab').onclick = () => {
            window.open(`project/${folder}/index.html`, '_blank');
        };
    }

    closeModal() {
        const modal = document.getElementById('projectModal');
        const frame = document.getElementById('projectFrame');
        
        modal.classList.add('hidden');
        frame.src = '';
    }

    filterProjects(searchTerm) {
        const filtered = this.projects.filter(project => 
            project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
            project.folder.toLowerCase().includes(searchTerm.toLowerCase())
        );
        
        const tempProjects = this.projects;
        this.projects = filtered;
        this.renderProjects();
        this.updateProjectCount();
        this.projects = tempProjects;
    }

    sortProjects(sortBy) {
        this.projects.sort((a, b) => {
            if (sortBy === 'name') {
                return a.name.localeCompare(b.name);
            } else if (sortBy === 'date') {
                return b.lastModified - a.lastModified;
            }
            return 0;
        });
        this.renderProjects();
    }

    toggleView() {
        const grid = document.getElementById('projectGrid');
        const button = document.getElementById('gridToggle');
        
        if (this.currentView === 'grid') {
            grid.className = 'space-y-4';
            button.textContent = '网格视图';
            this.currentView = 'list';
        } else {
            grid.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
            button.textContent = '列表视图';
            this.currentView = 'grid';
        }
        this.renderProjects();
    }

    updateProjectCount() {
        document.getElementById('projectCount').textContent = this.projects.length;
    }

    updateLastUpdate() {
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-CN');
    }

    formatDate(date) {
        return date.toLocaleDateString('zh-CN');
    }

    showLoading() {
        document.getElementById('loadingState').classList.remove('hidden');
        document.getElementById('projectGrid').classList.add('hidden');
        document.getElementById('emptyState').classList.add('hidden');
    }

    showEmptyState() {
        document.getElementById('loadingState').classList.add('hidden');
        document.getElementById('projectGrid').classList.add('hidden');
        document.getElementById('emptyState').classList.remove('hidden');
    }
}

// 初始化应用
const projectShowcase = new ProjectShowcase();
