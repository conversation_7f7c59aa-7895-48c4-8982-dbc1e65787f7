<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原型项目展示</title>
    <!-- 使用中国可访问的Tailwind CSS CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/tailwindcss/2.2.19/tailwind.min.js"></script>
    <link href="https://cdn.bootcdn.net/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <style>
        .project-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .loading {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-900">原型项目展示</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="refreshBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        刷新项目
                    </button>
                    <button id="gridToggle" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                        切换视图
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <!-- 搜索和过滤 -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" id="searchInput" placeholder="搜索项目..." 
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                </div>
                <select id="sortSelect" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="name">按名称排序</option>
                    <option value="date">按日期排序</option>
                </select>
            </div>
        </div>

        <!-- 项目统计 -->
        <div class="mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">项目概览</h2>
                        <p class="text-gray-600">共发现 <span id="projectCount" class="font-bold text-blue-600">0</span> 个原型项目</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">最后更新</p>
                        <p id="lastUpdate" class="text-sm font-medium text-gray-900">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-12">
            <div class="loading bg-gray-200 rounded-lg h-8 w-48 mx-auto mb-4"></div>
            <p class="text-gray-600">正在加载项目列表...</p>
        </div>

        <!-- 项目网格 -->
        <div id="projectGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 hidden">
            <!-- 项目卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 空状态 -->
        <div id="emptyState" class="text-center py-12 hidden">
            <div class="text-gray-400 mb-4">
                <svg class="mx-auto h-24 w-24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" 
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无项目</h3>
            <p class="text-gray-600 mb-4">在 project 文件夹中添加您的原型项目</p>
            <button onclick="location.reload()" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                重新加载
            </button>
        </div>
    </main>

    <!-- 项目详情模态框 -->
    <div id="projectModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-hidden">
                <div class="flex justify-between items-center p-6 border-b">
                    <h3 id="modalTitle" class="text-xl font-semibold">项目详情</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto max-h-96">
                    <iframe id="projectFrame" class="w-full h-96 border rounded-lg" src=""></iframe>
                </div>
                <div class="p-6 border-t bg-gray-50">
                    <div class="flex justify-between">
                        <button id="openInNewTab" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                            在新标签页打开
                        </button>
                        <button id="closeModalBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
